-- ========================================
-- ADVANCED KILLFEED CLIENT SCRIPT
-- ========================================

local isKillfeedEnabled = Config.EnableKillfeed
local isColorMenuOpen = false
local playerPed = PlayerPedId()
local playerId = PlayerId()

-- Variables
local recentDeaths = {}
local killfeedData = {}

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================

-- Get Player Distance
local function GetPlayerDistance(killer, victim)
    -- Validate entities
    if not killer or not victim then return nil end
    if not DoesEntityExist(killer) or not DoesEntityExist(victim) then return nil end

    -- Get coordinates with error handling
    local killerCoords = GetEntityCoords(killer)
    local victimCoords = GetEntityCoords(victim)

    -- Validate coordinates
    if not killerCoords or not victimCoords then return nil end

    -- Calculate distance
    local distance = #(killerCoords - victimCoords)

    -- Validate distance is reasonable (not negative or extremely large)
    if distance < 0 or distance > 10000 then return nil end

    -- Format distance based on unit preference
    if Config.DistanceUnit == "ft" then
        local distanceFt = math.floor(distance * 3.28084)
        return distanceFt .. " ft"
    else
        local distanceM = math.floor(distance)
        return distanceM .. " m"
    end
end

-- Get Weapon Info
local function GetWeaponInfo(weaponHash)
    -- Handle nil or invalid weapon hash
    if not weaponHash or weaponHash == 0 then
        return {
            name = "Unknown",
            icon = "unknown.png",
            category = "unknown"
        }
    end

    -- Search through weapon categories
    for weapon, info in pairs(Config.WeaponCategories) do
        local weaponHashKey = GetHashKey(weapon)
        if weaponHashKey == weaponHash then
            -- Validate the weapon info structure
            if info and info.name and info.icon then
                return {
                    name = info.name,
                    icon = info.icon,
                    category = info.category or "unknown"
                }
            end
        end
    end

    -- Check for common weapon hashes that might not be in config
    local commonWeapons = {
        [`WEAPON_UNARMED`] = {name = "Fists", icon = "fists.png", category = "melee"},
        [`WEAPON_RUN_OVER_BY_CAR`] = {name = "Vehicle", icon = "vehicle.png", category = "vehicle"},
        [`WEAPON_RAMMED_BY_CAR`] = {name = "Vehicle Ram", icon = "vehicle_ram.png", category = "vehicle"},
        [`WEAPON_FALL`] = {name = "Fall Damage", icon = "fall.png", category = "environmental"},
        [`WEAPON_EXPLOSION`] = {name = "Explosion", icon = "explosion.png", category = "explosive"},
        [`WEAPON_FIRE`] = {name = "Fire", icon = "fire.png", category = "environmental"},
        [`WEAPON_DROWNING`] = {name = "Drowning", icon = "drowning.png", category = "environmental"}
    }

    if commonWeapons[weaponHash] then
        return commonWeapons[weaponHash]
    end

    -- Final fallback with weapon hash for debugging
    return {
        name = "Unknown Weapon",
        icon = "unknown.png",
        category = "unknown",
        hash = weaponHash
    }
end

-- Check if Headshot
local function IsHeadshot(victim)
    if not Config.ShowHeadshot then return false end
    
    local found, bone = GetPedLastDamageBone(victim)
    if found then
        return bone == 31086 or bone == 39317 -- Head bones
    end
    return false
end

-- Check if Noscope (for sniper rifles)
local function IsNoscope(killer, weaponHash)
    if not Config.ShowNoscope then return false end
    
    local weaponInfo = GetWeaponInfo(weaponHash)
    if weaponInfo.category == "sniper" then
        return not IsFirstPersonAimCamActive()
    end
    return false
end

-- Check if Drive-by
local function IsDriveBy(killer)
    if not Config.ShowDriveBy then return false end
    
    local vehicle = GetVehiclePedIsIn(killer, false)
    return vehicle ~= 0 and GetPedInVehicleSeat(vehicle, -1) == killer
end

-- ========================================
-- KILLFEED FUNCTIONS
-- ========================================

-- Add Kill to Feed
local function AddKillToFeed(killerName, victimName, weaponInfo, distance, isHeadshot, isNoscope, isDriveBy, isSuicide)
    -- Validate required data
    if not victimName then
        if Config.Debug then
            print("^1[KILLFEED ERROR]^7 Missing victim name, skipping kill feed entry")
        end
        return
    end

    -- Ensure weapon info exists
    if not weaponInfo then
        weaponInfo = {
            name = "Unknown Weapon",
            icon = "unknown.png",
            category = "unknown"
        }
    end

    local killData = {
        id = GetGameTimer(),
        killer = killerName,
        victim = victimName,
        weapon = weaponInfo,
        distance = distance,
        headshot = isHeadshot or false,
        noscope = isNoscope or false,
        driveby = isDriveBy or false,
        suicide = isSuicide or false,
        timestamp = GetGameTimer()
    }

    -- Validate kill data before sending
    if not killData.victim or killData.victim == "" then
        if Config.Debug then
            print("^1[KILLFEED ERROR]^7 Invalid kill data, victim name is empty")
        end
        return
    end

    -- Send to UI with error handling
    local success, error = pcall(function()
        SendNUIMessage({
            action = "addKill",
            data = killData
        })
    end)

    if not success then
        if Config.Debug then
            print("^1[KILLFEED ERROR]^7 Failed to send kill data to UI: " .. tostring(error))
        end
        return
    end

    -- Play sound if enabled
    if Config.EnableSounds then
        local soundToPlay = nil

        if isHeadshot and Config.HeadshotSound then
            soundToPlay = Config.HeadshotSound
        elseif Config.KillSound then
            soundToPlay = Config.KillSound
        end

        if soundToPlay then
            pcall(function()
                SendNUIMessage({
                    action = "playSound",
                    sound = soundToPlay
                })
            end)
        end
    end

    -- Debug output
    if Config.Debug then
        local killerText = killerName or (isSuicide and "SUICIDE" or "Unknown")
        local weaponText = weaponInfo.name or "Unknown Weapon"
        local specialText = ""

        if isHeadshot then specialText = specialText .. " [HEADSHOT]" end
        if isNoscope then specialText = specialText .. " [NOSCOPE]" end
        if isDriveBy then specialText = specialText .. " [DRIVEBY]" end

        print(string.format("^2[KILLFEED]^7 %s killed %s with %s%s%s",
            killerText,
            victimName,
            weaponText,
            distance and (" at " .. distance) or "",
            specialText
        ))
    end
end

-- Handle Death Event
local function HandleDeath(killer, victim, weaponHash)
    if not isKillfeedEnabled then return end

    -- Validate entities
    if not victim or not DoesEntityExist(victim) then return end

    -- Create unique identifier for this death event
    local victimNetId = NetworkGetNetworkIdFromEntity(victim)
    local deathId = victimNetId .. "_" .. GetGameTimer()

    -- Prevent duplicate deaths using network ID instead of entity
    if recentDeaths[victimNetId] then return end
    recentDeaths[victimNetId] = true

    -- Set timer to clear recent death (increased time for better duplicate prevention)
    SetTimeout(3000, function()
        recentDeaths[victimNetId] = nil
    end)

    local killerName = nil
    local victimName = nil
    local distance = nil
    local isSuicide = false

    -- Get victim name with better error handling
    if IsPedAPlayer(victim) then
        local victimId = NetworkGetPlayerIndexFromPed(victim)
        if victimId and victimId >= 0 then
            victimName = GetPlayerName(victimId) or "Unknown Player"
        else
            victimName = "Unknown Player"
        end
    else
        if not Config.ShowNPCKills then return end
        victimName = "NPC"
    end

    -- Get killer info with improved validation
    if killer and killer ~= victim and DoesEntityExist(killer) then
        if IsPedAPlayer(killer) then
            local killerId = NetworkGetPlayerIndexFromPed(killer)
            if killerId and killerId >= 0 then
                killerName = GetPlayerName(killerId) or "Unknown Player"

                -- Calculate distance if enabled
                if Config.ShowDistance then
                    distance = GetPlayerDistance(killer, victim)
                end
            else
                killerName = "Unknown Player"
            end
        else
            if not Config.ShowNPCKills then return end
            killerName = "NPC"
        end
    else
        -- Suicide or environmental death
        isSuicide = true
        if not Config.ShowSuicide then return end
    end

    -- Get weapon info with fallback
    local weaponInfo = GetWeaponInfo(weaponHash)
    if not weaponInfo then
        weaponInfo = {
            name = "Unknown Weapon",
            icon = "unknown.png",
            category = "unknown"
        }
    end

    -- Check special kill types with error handling
    local headshot = false
    local noscope = false
    local driveby = false

    -- Safely check for headshot
    if victim and DoesEntityExist(victim) then
        headshot = IsHeadshot(victim)
    end

    -- Safely check for noscope and driveby
    if killer and DoesEntityExist(killer) then
        noscope = IsNoscope(killer, weaponHash)
        driveby = IsDriveBy(killer)
    end

    -- Add to killfeed with validation
    if victimName then
        AddKillToFeed(killerName, victimName, weaponInfo, distance, headshot, noscope, driveby, isSuicide)
    end
end

-- ========================================
-- EVENT HANDLERS
-- ========================================

-- Game Event Handler
AddEventHandler('gameEventTriggered', function(name, args)
    if name == 'CEventNetworkEntityDamage' then
        -- Validate args exist and have required data
        if not args or #args < 5 then return end

        local victim = args[1]
        local killer = args[2]
        local weaponHash = args[5] or 0
        local isFatal = args[4] == 1

        -- Additional validation
        if not victim or not DoesEntityExist(victim) then return end
        if not IsEntityAPed(victim) then return end
        if not isFatal then return end

        -- Check if victim is actually dead (additional safety check)
        if not IsEntityDead(victim) then return end

        -- Small delay to ensure proper data and prevent race conditions
        SetTimeout(150, function()
            -- Double-check entity still exists and is dead
            if DoesEntityExist(victim) and IsEntityDead(victim) then
                HandleDeath(killer, victim, weaponHash)
            end
        end)
    end
end)

-- Network Events
RegisterNetEvent('advanced_killfeed:toggle')
AddEventHandler('advanced_killfeed:toggle', function()
    isKillfeedEnabled = not isKillfeedEnabled
    SendNUIMessage({
        action = "toggleKillfeed",
        enabled = isKillfeedEnabled
    })
    
    local status = isKillfeedEnabled and "^2enabled^7" or "^1disabled^7"
    TriggerEvent('chat:addMessage', {
        color = {255, 255, 255},
        args = {"^3[Killfeed]^7 Killfeed " .. status}
    })
end)

-- Color Menu Events
RegisterNetEvent('advanced_killfeed:openColorMenu')
AddEventHandler('advanced_killfeed:openColorMenu', function()
    if not isColorMenuOpen then
        isColorMenuOpen = true
        SetNuiFocus(true, true)
        SendNUIMessage({
            action = "openColorMenu",
            colors = Config.DefaultColors
        })
    end
end)

RegisterNetEvent('advanced_killfeed:closeColorMenu')
AddEventHandler('advanced_killfeed:closeColorMenu', function()
    isColorMenuOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = "closeColorMenu"
    })
end)

RegisterNetEvent('advanced_killfeed:clearAll')
AddEventHandler('advanced_killfeed:clearAll', function()
    SendNUIMessage({
        action = "clearAll"
    })

    if Config.Debug then
        print("^2[KILLFEED]^7 Killfeed cleared")
    end
end)

-- ========================================
-- NUI CALLBACKS
-- ========================================

RegisterNUICallback('closeColorMenu', function(data, cb)
    TriggerEvent('advanced_killfeed:closeColorMenu')
    cb('ok')
end)

RegisterNUICallback('saveColors', function(data, cb)
    TriggerServerEvent('advanced_killfeed:saveColors', data)
    TriggerEvent('advanced_killfeed:closeColorMenu')
    cb('ok')
end)

RegisterNUICallback('resetColors', function(data, cb)
    SendNUIMessage({
        action = "updateColors",
        colors = Config.DefaultColors
    })
    cb('ok')
end)

-- ========================================
-- COMMANDS & KEY BINDINGS
-- ========================================

-- Toggle Killfeed Command
RegisterCommand('killfeed', function()
    TriggerEvent('advanced_killfeed:toggle')
end, false)

-- Color Menu Key Binding
RegisterCommand('killfeed_colors', function()
    TriggerServerEvent('advanced_killfeed:requestColorMenu')
end, false)

RegisterKeyMapping('killfeed_colors', 'Open Killfeed Color Menu', 'keyboard', Config.ColorMenuKey)

-- ========================================
-- INITIALIZATION
-- ========================================

CreateThread(function()
    -- Wait for game to load
    while not NetworkIsPlayerActive(PlayerId()) do
        Wait(100)
    end
    
    -- Initialize UI
    SendNUIMessage({
        action = "init",
        config = {
            showTime = Config.ShowTime,
            maxLines = Config.MaxKillLines,
            fadeAnimation = Config.FadeAnimation,
            colors = Config.DefaultColors
        }
    })
    
    if Config.Debug then
        print("^2[KILLFEED]^7 Advanced Killfeed initialized successfully!")
    end
end)
