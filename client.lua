-- ========================================
-- ADVANCED KILLFEED CLIENT SCRIPT
-- ========================================

local isKillfeedEnabled = Config.EnableKillfeed
local isColorMenuOpen = false
local playerPed = PlayerPedId()
local playerId = PlayerId()

-- Variables
local recentDeaths = {}
local killfeedData = {}

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================

-- Get Player Distance
local function GetPlayerDistance(killer, victim)
    if not killer or not victim then return nil end
    
    local killerCoords = GetEntityCoords(killer)
    local victimCoords = GetEntityCoords(victim)
    local distance = #(killerCoords - victimCoords)
    
    if Config.DistanceUnit == "ft" then
        distance = distance * 3.28084 -- Convert to feet
        return math.floor(distance) .. " ft"
    else
        return math.floor(distance) .. " m"
    end
end

-- Get Weapon Info
local function GetWeaponInfo(weaponHash)
    for weapon, info in pairs(Config.WeaponCategories) do
        if GetHashKey(weapon) == weaponHash then
            return info
        end
    end
    return { name = "Unknown", icon = "unknown.png", category = "unknown" }
end

-- Check if Headshot
local function IsHeadshot(victim)
    if not Config.ShowHeadshot then return false end
    
    local found, bone = GetPedLastDamageBone(victim)
    if found then
        return bone == 31086 or bone == 39317 -- Head bones
    end
    return false
end

-- Check if Noscope (for sniper rifles)
local function IsNoscope(killer, weaponHash)
    if not Config.ShowNoscope then return false end
    
    local weaponInfo = GetWeaponInfo(weaponHash)
    if weaponInfo.category == "sniper" then
        return not IsFirstPersonAimCamActive()
    end
    return false
end

-- Check if Drive-by
local function IsDriveBy(killer)
    if not Config.ShowDriveBy then return false end
    
    local vehicle = GetVehiclePedIsIn(killer, false)
    return vehicle ~= 0 and GetPedInVehicleSeat(vehicle, -1) == killer
end

-- ========================================
-- KILLFEED FUNCTIONS
-- ========================================

-- Add Kill to Feed
local function AddKillToFeed(killerName, victimName, weaponInfo, distance, isHeadshot, isNoscope, isDriveBy, isSuicide)
    local killData = {
        id = GetGameTimer(),
        killer = killerName,
        victim = victimName,
        weapon = weaponInfo,
        distance = distance,
        headshot = isHeadshot,
        noscope = isNoscope,
        driveby = isDriveBy,
        suicide = isSuicide,
        timestamp = GetGameTimer()
    }
    
    -- Send to UI
    SendNUIMessage({
        action = "addKill",
        data = killData
    })
    
    -- Play sound if enabled
    if Config.EnableSounds then
        if isHeadshot and Config.HeadshotSound then
            SendNUIMessage({
                action = "playSound",
                sound = Config.HeadshotSound
            })
        elseif Config.KillSound then
            SendNUIMessage({
                action = "playSound",
                sound = Config.KillSound
            })
        end
    end
    
    -- Debug print
    if Config.Debug then
        print(string.format("^2[KILLFEED]^7 %s killed %s with %s", killerName or "Unknown", victimName or "Unknown", weaponInfo.name))
    end
end

-- Handle Death Event
local function HandleDeath(killer, victim, weaponHash)
    if not isKillfeedEnabled then return end
    
    -- Prevent duplicate deaths
    if recentDeaths[victim] then return end
    recentDeaths[victim] = true
    
    -- Set timer to clear recent death
    SetTimeout(2000, function()
        recentDeaths[victim] = nil
    end)
    
    local killerName = nil
    local victimName = nil
    local distance = nil
    local isSuicide = false
    
    -- Get victim name
    if IsPedAPlayer(victim) then
        local victimId = NetworkGetPlayerIndexFromPed(victim)
        victimName = GetPlayerName(victimId)
    else
        if not Config.ShowNPCKills then return end
        victimName = "NPC"
    end
    
    -- Get killer info
    if killer and killer ~= victim and DoesEntityExist(killer) then
        if IsPedAPlayer(killer) then
            local killerId = NetworkGetPlayerIndexFromPed(killer)
            killerName = GetPlayerName(killerId)
            
            -- Calculate distance if enabled
            if Config.ShowDistance then
                distance = GetPlayerDistance(killer, victim)
            end
        else
            if not Config.ShowNPCKills then return end
            killerName = "NPC"
        end
    else
        -- Suicide or environmental death
        isSuicide = true
        if not Config.ShowSuicide then return end
    end
    
    -- Get weapon info
    local weaponInfo = GetWeaponInfo(weaponHash)
    
    -- Check special kill types
    local headshot = IsHeadshot(victim)
    local noscope = killer and IsNoscope(killer, weaponHash) or false
    local driveby = killer and IsDriveBy(killer) or false
    
    -- Add to killfeed
    AddKillToFeed(killerName, victimName, weaponInfo, distance, headshot, noscope, driveby, isSuicide)
end

-- ========================================
-- EVENT HANDLERS
-- ========================================

-- Game Event Handler
AddEventHandler('gameEventTriggered', function(name, args)
    if name == 'CEventNetworkEntityDamage' then
        local victim = args[1]
        local killer = args[2]
        local weaponHash = args[5]
        local isFatal = args[4] == 1
        
        if isFatal and IsEntityAPed(victim) then
            -- Small delay to ensure proper data
            SetTimeout(100, function()
                HandleDeath(killer, victim, weaponHash)
            end)
        end
    end
end)

-- Network Events
RegisterNetEvent('advanced_killfeed:toggle')
AddEventHandler('advanced_killfeed:toggle', function()
    isKillfeedEnabled = not isKillfeedEnabled
    SendNUIMessage({
        action = "toggleKillfeed",
        enabled = isKillfeedEnabled
    })
    
    local status = isKillfeedEnabled and "^2enabled^7" or "^1disabled^7"
    TriggerEvent('chat:addMessage', {
        color = {255, 255, 255},
        args = {"^3[Killfeed]^7 Killfeed " .. status}
    })
end)

-- Color Menu Events
RegisterNetEvent('advanced_killfeed:openColorMenu')
AddEventHandler('advanced_killfeed:openColorMenu', function()
    if not isColorMenuOpen then
        isColorMenuOpen = true
        SetNuiFocus(true, true)
        SendNUIMessage({
            action = "openColorMenu",
            colors = Config.DefaultColors
        })
    end
end)

RegisterNetEvent('advanced_killfeed:closeColorMenu')
AddEventHandler('advanced_killfeed:closeColorMenu', function()
    isColorMenuOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = "closeColorMenu"
    })
end)

-- ========================================
-- NUI CALLBACKS
-- ========================================

RegisterNUICallback('closeColorMenu', function(data, cb)
    TriggerEvent('advanced_killfeed:closeColorMenu')
    cb('ok')
end)

RegisterNUICallback('saveColors', function(data, cb)
    TriggerServerEvent('advanced_killfeed:saveColors', data)
    TriggerEvent('advanced_killfeed:closeColorMenu')
    cb('ok')
end)

RegisterNUICallback('resetColors', function(data, cb)
    SendNUIMessage({
        action = "updateColors",
        colors = Config.DefaultColors
    })
    cb('ok')
end)

-- ========================================
-- COMMANDS & KEY BINDINGS
-- ========================================

-- Toggle Killfeed Command
RegisterCommand('killfeed', function()
    TriggerEvent('advanced_killfeed:toggle')
end, false)

-- Color Menu Key Binding
RegisterCommand('killfeed_colors', function()
    TriggerServerEvent('advanced_killfeed:requestColorMenu')
end, false)

RegisterKeyMapping('killfeed_colors', 'Open Killfeed Color Menu', 'keyboard', Config.ColorMenuKey)

-- ========================================
-- INITIALIZATION
-- ========================================

CreateThread(function()
    -- Wait for game to load
    while not NetworkIsPlayerActive(PlayerId()) do
        Wait(100)
    end
    
    -- Initialize UI
    SendNUIMessage({
        action = "init",
        config = {
            showTime = Config.ShowTime,
            maxLines = Config.MaxKillLines,
            fadeAnimation = Config.FadeAnimation,
            colors = Config.DefaultColors
        }
    })
    
    if Config.Debug then
        print("^2[KILLFEED]^7 Advanced Killfeed initialized successfully!")
    end
end)
