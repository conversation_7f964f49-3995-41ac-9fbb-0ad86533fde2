-- ========================================
-- ADVANCED KILLFEED SERVER SCRIPT
-- ========================================

-- Variables
local playerColors = {}
local playerPermissions = {}

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================

-- Get Player Permission Level
local function GetPlayerPermissionLevel(source)
    -- Check if player has specific permissions
    if IsPlayerAceAllowed(source, "killfeed.owner") then
        return 4 -- Owner
    elseif IsPlayerAceAllowed(source, "killfeed.admin") then
        return 3 -- Admin
    elseif IsPlayerAceAllowed(source, "killfeed.moderator") then
        return 2 -- Moderator
    elseif IsPlayerAceAllowed(source, "killfeed.vip") then
        return 1 -- VIP
    else
        return 0 -- Regular player
    end
end

-- Check if Player Has Permission
local function HasPermission(source, requiredLevel)
    local playerLevel = GetPlayerPermissionLevel(source)
    local requiredLevelNum = Config.PermissionLevels[requiredLevel] or 0
    return playerLevel >= requiredLevelNum
end

-- Get Player Name
local function GetPlayerNameById(source)
    return GetPlayerName(source) or "Unknown Player"
end

-- ========================================
-- EVENT HANDLERS
-- ========================================

-- Player Connecting
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    
    -- Initialize player data
    playerColors[source] = Config.DefaultColors
    playerPermissions[source] = GetPlayerPermissionLevel(source)
    
    if Config.Debug then
        print(string.format("^2[KILLFEED]^7 Player %s connected with permission level %d", name, playerPermissions[source]))
    end
end)

-- Player Dropping
AddEventHandler('playerDropped', function(reason)
    local source = source
    
    -- Clean up player data
    playerColors[source] = nil
    playerPermissions[source] = nil
    
    if Config.Debug then
        print(string.format("^2[KILLFEED]^7 Player %s disconnected, data cleaned", GetPlayerNameById(source)))
    end
end)

-- Color Menu Request
RegisterNetEvent('advanced_killfeed:requestColorMenu')
AddEventHandler('advanced_killfeed:requestColorMenu', function()
    local source = source
    
    if not Config.EnableColorMenu then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            args = {"^1[Killfeed]^7 Color menu is disabled on this server."}
        })
        return
    end
    
    -- Check permission
    if not HasPermission(source, Config.ColorMenuPermission) then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            args = {"^1[Killfeed]^7 You don't have permission to access the color menu."}
        })
        return
    end
    
    -- Open color menu
    TriggerClientEvent('advanced_killfeed:openColorMenu', source)
    
    if Config.Debug then
        print(string.format("^2[KILLFEED]^7 %s opened color menu", GetPlayerNameById(source)))
    end
end)

-- Save Colors
RegisterNetEvent('advanced_killfeed:saveColors')
AddEventHandler('advanced_killfeed:saveColors', function(colors)
    local source = source
    
    -- Validate permission
    if not HasPermission(source, Config.ColorMenuPermission) then
        return
    end
    
    -- Validate colors data
    if not colors or type(colors) ~= "table" then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            args = {"^1[Killfeed]^7 Invalid color data received."}
        })
        return
    end
    
    -- Save colors for player
    playerColors[source] = colors
    
    -- Broadcast to all players (or just the player who changed it)
    if Config.ColorMenuPermission == "all" then
        -- If everyone can change colors, only update for that player
        TriggerClientEvent('advanced_killfeed:updateColors', source, colors)
    else
        -- If only admins can change, update for everyone
        TriggerClientEvent('advanced_killfeed:updateColors', -1, colors)
    end
    
    -- Success message
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        args = {"^2[Killfeed]^7 Colors saved successfully!"}
    })
    
    if Config.Debug then
        print(string.format("^2[KILLFEED]^7 %s saved new colors", GetPlayerNameById(source)))
    end
end)

-- Get Player Colors
RegisterNetEvent('advanced_killfeed:getColors')
AddEventHandler('advanced_killfeed:getColors', function()
    local source = source
    local colors = playerColors[source] or Config.DefaultColors
    
    TriggerClientEvent('advanced_killfeed:receiveColors', source, colors)
end)

-- Admin Commands
RegisterNetEvent('advanced_killfeed:adminCommand')
AddEventHandler('advanced_killfeed:adminCommand', function(command, target, data)
    local source = source
    
    -- Check admin permission
    if not HasPermission(source, "admin") then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            args = {"^1[Killfeed]^7 You don't have permission to use admin commands."}
        })
        return
    end
    
    if command == "reset_colors" then
        if target == "all" then
            -- Reset colors for all players
            for playerId, _ in pairs(playerColors) do
                playerColors[playerId] = Config.DefaultColors
                TriggerClientEvent('advanced_killfeed:updateColors', playerId, Config.DefaultColors)
            end
            
            TriggerClientEvent('chat:addMessage', -1, {
                color = {255, 255, 0},
                args = {"^3[Killfeed]^7 Colors have been reset to default by an administrator."}
            })
        else
            -- Reset colors for specific player
            local targetId = tonumber(target)
            if targetId and playerColors[targetId] then
                playerColors[targetId] = Config.DefaultColors
                TriggerClientEvent('advanced_killfeed:updateColors', targetId, Config.DefaultColors)
                
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    args = {"^2[Killfeed]^7 Colors reset for player " .. GetPlayerNameById(targetId)}
                })
            end
        end
    elseif command == "toggle_killfeed" then
        if target == "all" then
            TriggerClientEvent('advanced_killfeed:toggle', -1)
        else
            local targetId = tonumber(target)
            if targetId then
                TriggerClientEvent('advanced_killfeed:toggle', targetId)
            end
        end
    end
end)

-- ========================================
-- COMMANDS
-- ========================================

-- Admin Reset Colors Command
RegisterCommand('killfeed_reset', function(source, args, rawCommand)
    if source == 0 then -- Console
        -- Reset all colors
        for playerId, _ in pairs(playerColors) do
            playerColors[playerId] = Config.DefaultColors
            TriggerClientEvent('advanced_killfeed:updateColors', playerId, Config.DefaultColors)
        end
        print("^2[KILLFEED]^7 All player colors reset to default")
    else
        -- Player command
        if HasPermission(source, "admin") then
            local target = args[1] or "self"
            
            if target == "all" then
                TriggerEvent('advanced_killfeed:adminCommand', source, "reset_colors", "all")
            else
                local targetId = tonumber(target) or source
                TriggerEvent('advanced_killfeed:adminCommand', source, "reset_colors", tostring(targetId))
            end
        else
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                args = {"^1[Killfeed]^7 You don't have permission to use this command."}
            })
        end
    end
end, false)

-- Get Permission Command (for debugging)
RegisterCommand('killfeed_permission', function(source, args, rawCommand)
    if source ~= 0 then
        local level = GetPlayerPermissionLevel(source)
        local levelName = "Unknown"
        
        for name, num in pairs(Config.PermissionLevels) do
            if num == level then
                levelName = name
                break
            end
        end
        
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 255},
            args = {"^3[Killfeed]^7 Your permission level: " .. levelName .. " (" .. level .. ")"}
        })
    end
end, false)

-- ========================================
-- INITIALIZATION
-- ========================================

CreateThread(function()
    -- Server startup message
    print("^2========================================^7")
    print("^2    ADVANCED KILLFEED SERVER LOADED    ^7")
    print("^2========================================^7")
    print("^3Version:^7 " .. GetResourceMetadata(GetCurrentResourceName(), 'version', 0))
    print("^3Author:^7 " .. GetResourceMetadata(GetCurrentResourceName(), 'author', 0))
    print("^3Color Menu:^7 " .. (Config.EnableColorMenu and "^2Enabled^7" or "^1Disabled^7"))
    print("^3Custom Images:^7 " .. (Config.EnableCustomImages and "^2Enabled^7" or "^1Disabled^7"))
    print("^3Distance Display:^7 " .. (Config.ShowDistance and "^2Enabled^7" or "^1Disabled^7"))
    print("^2========================================^7")
end)
