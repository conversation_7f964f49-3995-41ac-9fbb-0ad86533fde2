// ========================================
// ADVANCED KILLFEED JAVASCRIPT
// ========================================

class AdvancedKillfeed {
    constructor() {
        this.config = {
            showTime: 8000,
            maxLines: 6,
            fadeAnimation: true,
            colors: {
                background: "rgba(0, 0, 0, 0.8)",
                killer: "#00FF00",
                victim: "#FF0000",
                weapon: "#FFFFFF",
                distance: "#FFD700",
                border: "#333333"
            }
        };
        
        this.killEntries = [];
        this.isColorMenuOpen = false;
        this.customImages = new Map();
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupColorMenu();
        this.loadCustomImages();

        // Show killfeed by default
        const container = document.getElementById('killfeed-container');
        container.classList.remove('hidden');

        console.log('Advanced Killfeed initialized');
    }

    setupEventListeners() {
        // Listen for messages from FiveM
        window.addEventListener('message', (event) => {
            this.handleMessage(event.data);
        });

        // Color menu events
        document.getElementById('close-color-menu').addEventListener('click', () => {
            this.closeColorMenu();
        });

        document.getElementById('save-colors').addEventListener('click', () => {
            this.saveColors();
        });

        document.getElementById('reset-colors').addEventListener('click', () => {
            this.resetColors();
        });

        // Custom image upload
        document.getElementById('custom-image-upload').addEventListener('change', (e) => {
            this.handleImageUpload(e);
        });

        // Preset color selection
        document.querySelectorAll('.preset-color').forEach(preset => {
            preset.addEventListener('click', () => {
                this.applyPreset(preset.dataset.preset);
            });
        });

        // Color picker changes
        document.querySelectorAll('.color-picker').forEach(picker => {
            picker.addEventListener('change', () => {
                this.updatePreview();
            });
        });

        // Opacity slider
        document.getElementById('bg-opacity').addEventListener('input', (e) => {
            document.querySelector('.opacity-label').textContent = e.target.value + '%';
            this.updatePreview();
        });
    }

    handleMessage(data) {
        switch (data.action) {
            case 'init':
                this.config = { ...this.config, ...data.config };
                this.applyColors(this.config.colors);
                this.setPosition(this.config.position || 'left');
                break;
                
            case 'addKill':
                this.addKill(data.data);
                break;
                
            case 'toggleKillfeed':
                this.toggleKillfeed(data.enabled);
                break;
                
            case 'openColorMenu':
                this.openColorMenu(data.colors);
                break;
                
            case 'closeColorMenu':
                this.closeColorMenu();
                break;
                
            case 'updateColors':
                this.applyColors(data.colors);
                break;
                
            case 'playSound':
                this.playSound(data.sound);
                break;

            case 'clearAll':
                this.clearAllKills();
                break;
        }
    }

    clearAllKills() {
        try {
            // Clear all kill entries with animation
            this.killEntries.forEach(entry => {
                if (entry && entry.parentNode) {
                    this.removeKillEntry(entry);
                }
            });

            // Clear the array
            this.killEntries = [];

            console.log('Killfeed cleared');
        } catch (error) {
            console.error('Error clearing killfeed:', error);
        }
    }

    addKill(killData) {
        try {
            const killfeedList = document.getElementById('killfeed-list');
            if (!killfeedList) {
                console.error('Killfeed list element not found');
                return;
            }

            // Validate kill data
            if (!killData || !killData.victim) {
                console.error('Invalid kill data received:', killData);
                return;
            }

            // Remove oldest entries if we exceed max lines
            while (this.killEntries.length >= this.config.maxLines) {
                const oldEntry = this.killEntries.shift();
                if (oldEntry && oldEntry.parentNode) {
                    this.removeKillEntry(oldEntry);
                }
            }

            // Create kill entry element
            const killEntry = this.createKillEntry(killData);
            if (!killEntry) {
                console.error('Failed to create kill entry');
                return;
            }

            // Add fade-in animation
            killEntry.style.opacity = '0';
            killEntry.style.transform = 'translateX(-100%)';

            // Add to list
            killfeedList.appendChild(killEntry);
            this.killEntries.push(killEntry);

            // Trigger fade-in animation
            requestAnimationFrame(() => {
                killEntry.style.transition = 'all 0.5s ease';
                killEntry.style.opacity = '1';
                killEntry.style.transform = 'translateX(0)';
            });

            // Auto-remove after showTime
            const removeTimeout = setTimeout(() => {
                this.removeKillEntry(killEntry);
            }, this.config.showTime);

            // Store timeout reference for potential cleanup
            killEntry.removeTimeout = removeTimeout;

        } catch (error) {
            console.error('Error adding kill to feed:', error);
        }
    }

    removeKillEntry(killEntry) {
        if (!killEntry || !killEntry.parentNode) return;

        // Clear any pending timeout
        if (killEntry.removeTimeout) {
            clearTimeout(killEntry.removeTimeout);
        }

        // Add fade-out animation
        killEntry.style.transition = 'all 0.5s ease';
        killEntry.style.opacity = '0';
        killEntry.style.transform = 'translateX(-100%)';

        // Remove from DOM after animation
        setTimeout(() => {
            if (killEntry.parentNode) {
                killEntry.parentNode.removeChild(killEntry);
            }

            // Remove from array
            const index = this.killEntries.indexOf(killEntry);
            if (index > -1) {
                this.killEntries.splice(index, 1);
            }
        }, 500);
    }

    createKillEntry(killData) {
        try {
            const entry = document.createElement('div');
            entry.className = 'kill-entry';

            // Validate required data
            if (!killData.victim) {
                console.error('Kill data missing victim name');
                return null;
            }

            // Add special classes
            if (killData.suicide) entry.classList.add('suicide');
            if (killData.headshot) entry.classList.add('headshot');
            if (killData.noscope) entry.classList.add('noscope');
            if (killData.driveby) entry.classList.add('driveby');

            // Killer info
            const killerInfo = document.createElement('div');
            killerInfo.className = 'killer-info';

            if (killData.killer && !killData.suicide) {
                const killerName = document.createElement('span');
                killerName.className = 'player-name killer-name';
                killerName.textContent = killData.killer;
                killerName.style.color = this.config.colors.killer || '#00FF00';
                killerInfo.appendChild(killerName);
            }

            // Weapon info
            const weaponInfo = document.createElement('div');
            weaponInfo.className = 'weapon-info';

            // Weapon icon with error handling
            if (killData.weapon) {
                const weaponIcon = document.createElement('img');
                weaponIcon.className = 'weapon-icon';
                weaponIcon.src = this.getWeaponIcon(killData.weapon);
                weaponIcon.alt = killData.weapon.name || 'Unknown Weapon';

                // Handle image load errors
                weaponIcon.onerror = () => {
                    weaponIcon.src = 'images/unknown.png';
                };

                weaponInfo.appendChild(weaponIcon);
            }

            // Special kill indicators
            if (killData.headshot) {
                const headshotIcon = document.createElement('i');
                headshotIcon.className = 'fas fa-crosshairs headshot-icon';
                headshotIcon.title = 'Headshot';
                weaponInfo.appendChild(headshotIcon);
            }

            if (killData.noscope) {
                const noscopeIcon = document.createElement('i');
                noscopeIcon.className = 'fas fa-eye-slash noscope-icon';
                noscopeIcon.title = 'No Scope';
                weaponInfo.appendChild(noscopeIcon);
            }

            if (killData.driveby) {
                const drivebyIcon = document.createElement('i');
                drivebyIcon.className = 'fas fa-car driveby-icon';
                drivebyIcon.title = 'Drive-by';
                weaponInfo.appendChild(drivebyIcon);
            }

            // Victim info
            const victimInfo = document.createElement('div');
            victimInfo.className = 'victim-info';

            const victimName = document.createElement('span');
            victimName.className = 'player-name victim-name';
            victimName.textContent = killData.victim;
            victimName.style.color = this.config.colors.victim || '#FF0000';
            victimInfo.appendChild(victimName);

            // Distance info
            if (killData.distance) {
                const distanceInfo = document.createElement('span');
                distanceInfo.className = 'distance-info';
                distanceInfo.textContent = killData.distance;
                distanceInfo.style.color = this.config.colors.distance || '#FFD700';
                victimInfo.appendChild(distanceInfo);
            }

            // Assemble entry
            entry.appendChild(killerInfo);
            entry.appendChild(weaponInfo);
            entry.appendChild(victimInfo);

            return entry;

        } catch (error) {
            console.error('Error creating kill entry:', error);
            return null;
        }
    }

    getWeaponIcon(weapon) {
        // Check for custom image first
        if (this.customImages.has(weapon.icon)) {
            return this.customImages.get(weapon.icon);
        }
        
        // Default weapon icon path
        return `images/${weapon.icon}`;
    }

    toggleKillfeed(enabled) {
        const container = document.getElementById('killfeed-container');
        if (enabled) {
            container.classList.remove('hidden');
        } else {
            container.classList.add('hidden');
        }
    }

    setPosition(position) {
        const container = document.getElementById('killfeed-container');
        if (position === 'right') {
            container.style.left = 'auto';
            container.style.right = '20px';
        } else {
            container.style.left = '20px';
            container.style.right = 'auto';
        }
    }

    // Color Menu Functions
    openColorMenu(colors = null) {
        if (colors) {
            this.loadColorsToMenu(colors);
        }
        
        document.getElementById('color-menu').classList.remove('hidden');
        this.isColorMenuOpen = true;
        this.updatePreview();
    }

    closeColorMenu() {
        document.getElementById('color-menu').classList.add('hidden');
        this.isColorMenuOpen = false;
        
        // Send close message to FiveM
        fetch(`https://${GetParentResourceName()}/closeColorMenu`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }

    loadColorsToMenu(colors) {
        document.getElementById('killer-color').value = colors.killer || '#00FF00';
        document.getElementById('victim-color').value = colors.victim || '#FF0000';
        document.getElementById('weapon-color').value = colors.weapon || '#FFFFFF';
        document.getElementById('distance-color').value = colors.distance || '#FFD700';
        document.getElementById('border-color').value = colors.border || '#333333';
        
        // Parse background color and opacity
        const bgMatch = colors.background.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/);
        if (bgMatch) {
            const [, r, g, b, a] = bgMatch;
            const hex = '#' + [r, g, b].map(x => parseInt(x).toString(16).padStart(2, '0')).join('');
            document.getElementById('bg-color').value = hex;
            document.getElementById('bg-opacity').value = Math.round((a || 1) * 100);
            document.querySelector('.opacity-label').textContent = Math.round((a || 1) * 100) + '%';
        }
    }

    saveColors() {
        const colors = this.getColorsFromMenu();
        
        // Send to FiveM
        fetch(`https://${GetParentResourceName()}/saveColors`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(colors)
        });
        
        this.applyColors(colors);
        this.closeColorMenu();
    }

    resetColors() {
        const defaultColors = {
            background: "rgba(0, 0, 0, 0.8)",
            killer: "#00FF00",
            victim: "#FF0000",
            weapon: "#FFFFFF",
            distance: "#FFD700",
            border: "#333333"
        };
        
        this.loadColorsToMenu(defaultColors);
        this.updatePreview();
    }

    getColorsFromMenu() {
        const bgColor = document.getElementById('bg-color').value;
        const bgOpacity = document.getElementById('bg-opacity').value / 100;
        
        // Convert hex to rgba
        const r = parseInt(bgColor.substr(1, 2), 16);
        const g = parseInt(bgColor.substr(3, 2), 16);
        const b = parseInt(bgColor.substr(5, 2), 16);
        
        return {
            background: `rgba(${r}, ${g}, ${b}, ${bgOpacity})`,
            killer: document.getElementById('killer-color').value,
            victim: document.getElementById('victim-color').value,
            weapon: document.getElementById('weapon-color').value,
            distance: document.getElementById('distance-color').value,
            border: document.getElementById('border-color').value
        };
    }

    applyColors(colors) {
        this.config.colors = colors;
        
        // Apply to killfeed container
        const container = document.getElementById('killfeed-container');
        container.style.background = colors.background;
        container.style.borderColor = colors.border;
        
        // Apply to existing entries
        document.querySelectorAll('.killer-name').forEach(el => {
            el.style.color = colors.killer;
        });
        
        document.querySelectorAll('.victim-name').forEach(el => {
            el.style.color = colors.victim;
        });
        
        document.querySelectorAll('.distance-info').forEach(el => {
            el.style.color = colors.distance;
        });
    }

    updatePreview() {
        try {
            const colors = this.getColorsFromMenu();
            if (!colors) return;

            // Update preview elements with null checks
            const killerPreview = document.querySelector('.killer-preview');
            if (killerPreview) killerPreview.style.color = colors.killer;

            const victimPreview = document.querySelector('.victim-preview');
            if (victimPreview) victimPreview.style.color = colors.victim;

            const weaponPreview = document.querySelector('.weapon-preview');
            if (weaponPreview) weaponPreview.style.color = colors.weapon;

            const distancePreview = document.querySelector('.distance-preview');
            if (distancePreview) distancePreview.style.color = colors.distance;

            const borderPreview = document.querySelector('.border-preview');
            if (borderPreview) borderPreview.style.borderColor = colors.border;

            // Update preview killfeed
            const preview = document.querySelector('.killfeed-preview');
            if (preview) {
                preview.style.background = colors.background;
                preview.style.borderColor = colors.border;
            }

            const previewEntry = document.querySelector('.preview-entry');
            if (previewEntry) {
                previewEntry.style.borderLeftColor = colors.border;
            }

            const previewKillerName = document.querySelector('.preview-entry .killer-name');
            if (previewKillerName) previewKillerName.style.color = colors.killer;

            const previewVictimName = document.querySelector('.preview-entry .victim-name');
            if (previewVictimName) previewVictimName.style.color = colors.victim;

            const previewDistanceInfo = document.querySelector('.preview-entry .distance-info');
            if (previewDistanceInfo) previewDistanceInfo.style.color = colors.distance;

        } catch (error) {
            console.error('Error updating preview:', error);
        }
    }

    applyPreset(presetName) {
        const presets = {
            default: {
                background: "rgba(0, 0, 0, 0.8)",
                killer: "#00FF00",
                victim: "#FF0000",
                weapon: "#FFFFFF",
                distance: "#FFD700",
                border: "#333333"
            },
            dark: {
                background: "rgba(26, 26, 26, 0.9)",
                killer: "#4CAF50",
                victim: "#F44336",
                weapon: "#E0E0E0",
                distance: "#FFC107",
                border: "#424242"
            },
            neon: {
                background: "rgba(255, 0, 110, 0.2)",
                killer: "#00FFFF",
                victim: "#FF1493",
                weapon: "#FFFFFF",
                distance: "#FFFF00",
                border: "#8338EC"
            },
            military: {
                background: "rgba(45, 80, 22, 0.8)",
                killer: "#90EE90",
                victim: "#CD5C5C",
                weapon: "#F5F5DC",
                distance: "#FFD700",
                border: "#556B2F"
            }
        };
        
        if (presets[presetName]) {
            this.loadColorsToMenu(presets[presetName]);
            this.updatePreview();
        }
    }

    // Custom Images
    handleImageUpload(event) {
        const files = event.target.files;
        
        for (let file of files) {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.addCustomImage(file.name, e.target.result);
                };
                reader.readAsDataURL(file);
            }
        }
    }

    addCustomImage(filename, dataUrl) {
        this.customImages.set(filename, dataUrl);
        
        const container = document.getElementById('uploaded-images');
        const imageDiv = document.createElement('div');
        imageDiv.className = 'uploaded-image';
        
        const img = document.createElement('img');
        img.src = dataUrl;
        img.alt = filename;
        
        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-image';
        removeBtn.innerHTML = '×';
        removeBtn.onclick = () => {
            this.customImages.delete(filename);
            container.removeChild(imageDiv);
        };
        
        imageDiv.appendChild(img);
        imageDiv.appendChild(removeBtn);
        container.appendChild(imageDiv);
    }

    loadCustomImages() {
        // Load any saved custom images from localStorage
        const saved = localStorage.getItem('killfeed_custom_images');
        if (saved) {
            try {
                const images = JSON.parse(saved);
                for (let [filename, dataUrl] of Object.entries(images)) {
                    this.customImages.set(filename, dataUrl);
                    this.addCustomImage(filename, dataUrl);
                }
            } catch (e) {
                console.error('Failed to load custom images:', e);
            }
        }
    }

    saveCustomImages() {
        const images = Object.fromEntries(this.customImages);
        localStorage.setItem('killfeed_custom_images', JSON.stringify(images));
    }

    playSound(soundFile) {
        try {
            if (!soundFile) return;

            // Try to find existing audio element first
            let audioId = soundFile.replace('.mp3', '-sound').replace('.wav', '-sound').replace('.ogg', '-sound');
            let audio = document.getElementById(audioId);

            if (!audio) {
                // Create audio element dynamically if not found
                audio = document.createElement('audio');
                audio.id = audioId;
                audio.src = `sounds/${soundFile}`;
                audio.preload = 'auto';
                audio.volume = 0.5; // Set reasonable volume
                document.body.appendChild(audio);
            }

            // Reset and play
            audio.currentTime = 0;
            audio.play().catch(e => {
                console.warn('Sound play failed:', e);
                // Try alternative sound path
                if (!audio.src.includes('sounds/')) {
                    audio.src = `sounds/${soundFile}`;
                    audio.play().catch(err => console.warn('Alternative sound path also failed:', err));
                }
            });

        } catch (error) {
            console.error('Error playing sound:', error);
        }
    }
}

// Helper function for FiveM resource name
function GetParentResourceName() {
    return window.location.hostname === 'nui-game-internal' ? 
           'advanced_killfeed' : 'advanced_killfeed';
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.killfeed = new AdvancedKillfeed();
});
