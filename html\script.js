// ========================================
// ADVANCED KILLFEED JAVASCRIPT
// ========================================

class AdvancedKillfeed {
    constructor() {
        this.config = {
            showTime: 8000,
            maxLines: 6,
            fadeAnimation: true,
            colors: {
                background: "rgba(0, 0, 0, 0.8)",
                killer: "#00FF00",
                victim: "#FF0000",
                weapon: "#FFFFFF",
                distance: "#FFD700",
                border: "#333333"
            }
        };
        
        this.killEntries = [];
        this.isColorMenuOpen = false;
        this.customImages = new Map();
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupColorMenu();
        this.loadCustomImages();
        
        // Hide killfeed initially
        document.getElementById('killfeed-container').classList.add('hidden');
        
        console.log('Advanced Killfeed initialized');
    }

    setupEventListeners() {
        // Listen for messages from FiveM
        window.addEventListener('message', (event) => {
            this.handleMessage(event.data);
        });

        // Color menu events
        document.getElementById('close-color-menu').addEventListener('click', () => {
            this.closeColorMenu();
        });

        document.getElementById('save-colors').addEventListener('click', () => {
            this.saveColors();
        });

        document.getElementById('reset-colors').addEventListener('click', () => {
            this.resetColors();
        });

        // Custom image upload
        document.getElementById('custom-image-upload').addEventListener('change', (e) => {
            this.handleImageUpload(e);
        });

        // Preset color selection
        document.querySelectorAll('.preset-color').forEach(preset => {
            preset.addEventListener('click', () => {
                this.applyPreset(preset.dataset.preset);
            });
        });

        // Color picker changes
        document.querySelectorAll('.color-picker').forEach(picker => {
            picker.addEventListener('change', () => {
                this.updatePreview();
            });
        });

        // Opacity slider
        document.getElementById('bg-opacity').addEventListener('input', (e) => {
            document.querySelector('.opacity-label').textContent = e.target.value + '%';
            this.updatePreview();
        });
    }

    handleMessage(data) {
        switch (data.action) {
            case 'init':
                this.config = { ...this.config, ...data.config };
                this.applyColors(this.config.colors);
                break;
                
            case 'addKill':
                this.addKill(data.data);
                break;
                
            case 'toggleKillfeed':
                this.toggleKillfeed(data.enabled);
                break;
                
            case 'openColorMenu':
                this.openColorMenu(data.colors);
                break;
                
            case 'closeColorMenu':
                this.closeColorMenu();
                break;
                
            case 'updateColors':
                this.applyColors(data.colors);
                break;
                
            case 'playSound':
                this.playSound(data.sound);
                break;
        }
    }

    addKill(killData) {
        const killfeedList = document.getElementById('killfeed-list');
        
        // Remove oldest entries if we exceed max lines
        while (this.killEntries.length >= this.config.maxLines) {
            const oldEntry = this.killEntries.shift();
            if (oldEntry && oldEntry.parentNode) {
                oldEntry.classList.add('fade-out');
                setTimeout(() => {
                    if (oldEntry.parentNode) {
                        oldEntry.parentNode.removeChild(oldEntry);
                    }
                }, 500);
            }
        }

        // Create kill entry element
        const killEntry = this.createKillEntry(killData);
        
        // Add to list
        killfeedList.appendChild(killEntry);
        this.killEntries.push(killEntry);

        // Auto-remove after showTime
        setTimeout(() => {
            if (killEntry && killEntry.parentNode) {
                killEntry.classList.add('fade-out');
                setTimeout(() => {
                    if (killEntry.parentNode) {
                        killEntry.parentNode.removeChild(killEntry);
                    }
                    const index = this.killEntries.indexOf(killEntry);
                    if (index > -1) {
                        this.killEntries.splice(index, 1);
                    }
                }, 500);
            }
        }, this.config.showTime);
    }

    createKillEntry(killData) {
        const entry = document.createElement('div');
        entry.className = 'kill-entry';
        
        // Add special classes
        if (killData.suicide) entry.classList.add('suicide');
        if (killData.headshot) entry.classList.add('headshot');
        if (killData.noscope) entry.classList.add('noscope');
        if (killData.driveby) entry.classList.add('driveby');

        // Killer info
        const killerInfo = document.createElement('div');
        killerInfo.className = 'killer-info';
        
        if (killData.killer && !killData.suicide) {
            const killerName = document.createElement('span');
            killerName.className = 'player-name killer-name';
            killerName.textContent = killData.killer;
            killerName.style.color = this.config.colors.killer;
            killerInfo.appendChild(killerName);
        }

        // Weapon info
        const weaponInfo = document.createElement('div');
        weaponInfo.className = 'weapon-info';
        
        // Weapon icon
        const weaponIcon = document.createElement('img');
        weaponIcon.className = 'weapon-icon';
        weaponIcon.src = this.getWeaponIcon(killData.weapon);
        weaponIcon.alt = killData.weapon.name;
        weaponInfo.appendChild(weaponIcon);

        // Special kill indicators
        if (killData.headshot) {
            const headshotIcon = document.createElement('i');
            headshotIcon.className = 'fas fa-crosshairs headshot-icon';
            headshotIcon.title = 'Headshot';
            weaponInfo.appendChild(headshotIcon);
        }

        if (killData.noscope) {
            const noscopeIcon = document.createElement('i');
            noscopeIcon.className = 'fas fa-eye-slash noscope-icon';
            noscopeIcon.title = 'No Scope';
            weaponInfo.appendChild(noscopeIcon);
        }

        if (killData.driveby) {
            const drivebyIcon = document.createElement('i');
            drivebyIcon.className = 'fas fa-car driveby-icon';
            drivebyIcon.title = 'Drive-by';
            weaponInfo.appendChild(drivebyIcon);
        }

        // Victim info
        const victimInfo = document.createElement('div');
        victimInfo.className = 'victim-info';
        
        const victimName = document.createElement('span');
        victimName.className = 'player-name victim-name';
        victimName.textContent = killData.victim;
        victimName.style.color = this.config.colors.victim;
        victimInfo.appendChild(victimName);

        // Distance info
        if (killData.distance) {
            const distanceInfo = document.createElement('span');
            distanceInfo.className = 'distance-info';
            distanceInfo.textContent = killData.distance;
            distanceInfo.style.color = this.config.colors.distance;
            victimInfo.appendChild(distanceInfo);
        }

        // Assemble entry
        entry.appendChild(killerInfo);
        entry.appendChild(weaponInfo);
        entry.appendChild(victimInfo);

        return entry;
    }

    getWeaponIcon(weapon) {
        // Check for custom image first
        if (this.customImages.has(weapon.icon)) {
            return this.customImages.get(weapon.icon);
        }
        
        // Default weapon icon path
        return `images/${weapon.icon}`;
    }

    toggleKillfeed(enabled) {
        const container = document.getElementById('killfeed-container');
        if (enabled) {
            container.classList.remove('hidden');
        } else {
            container.classList.add('hidden');
        }
    }

    // Color Menu Functions
    openColorMenu(colors = null) {
        if (colors) {
            this.loadColorsToMenu(colors);
        }
        
        document.getElementById('color-menu').classList.remove('hidden');
        this.isColorMenuOpen = true;
        this.updatePreview();
    }

    closeColorMenu() {
        document.getElementById('color-menu').classList.add('hidden');
        this.isColorMenuOpen = false;
        
        // Send close message to FiveM
        fetch(`https://${GetParentResourceName()}/closeColorMenu`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }

    loadColorsToMenu(colors) {
        document.getElementById('killer-color').value = colors.killer || '#00FF00';
        document.getElementById('victim-color').value = colors.victim || '#FF0000';
        document.getElementById('weapon-color').value = colors.weapon || '#FFFFFF';
        document.getElementById('distance-color').value = colors.distance || '#FFD700';
        document.getElementById('border-color').value = colors.border || '#333333';
        
        // Parse background color and opacity
        const bgMatch = colors.background.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/);
        if (bgMatch) {
            const [, r, g, b, a] = bgMatch;
            const hex = '#' + [r, g, b].map(x => parseInt(x).toString(16).padStart(2, '0')).join('');
            document.getElementById('bg-color').value = hex;
            document.getElementById('bg-opacity').value = Math.round((a || 1) * 100);
            document.querySelector('.opacity-label').textContent = Math.round((a || 1) * 100) + '%';
        }
    }

    saveColors() {
        const colors = this.getColorsFromMenu();
        
        // Send to FiveM
        fetch(`https://${GetParentResourceName()}/saveColors`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(colors)
        });
        
        this.applyColors(colors);
        this.closeColorMenu();
    }

    resetColors() {
        const defaultColors = {
            background: "rgba(0, 0, 0, 0.8)",
            killer: "#00FF00",
            victim: "#FF0000",
            weapon: "#FFFFFF",
            distance: "#FFD700",
            border: "#333333"
        };
        
        this.loadColorsToMenu(defaultColors);
        this.updatePreview();
    }

    getColorsFromMenu() {
        const bgColor = document.getElementById('bg-color').value;
        const bgOpacity = document.getElementById('bg-opacity').value / 100;
        
        // Convert hex to rgba
        const r = parseInt(bgColor.substr(1, 2), 16);
        const g = parseInt(bgColor.substr(3, 2), 16);
        const b = parseInt(bgColor.substr(5, 2), 16);
        
        return {
            background: `rgba(${r}, ${g}, ${b}, ${bgOpacity})`,
            killer: document.getElementById('killer-color').value,
            victim: document.getElementById('victim-color').value,
            weapon: document.getElementById('weapon-color').value,
            distance: document.getElementById('distance-color').value,
            border: document.getElementById('border-color').value
        };
    }

    applyColors(colors) {
        this.config.colors = colors;
        
        // Apply to killfeed container
        const container = document.getElementById('killfeed-container');
        container.style.background = colors.background;
        container.style.borderColor = colors.border;
        
        // Apply to existing entries
        document.querySelectorAll('.killer-name').forEach(el => {
            el.style.color = colors.killer;
        });
        
        document.querySelectorAll('.victim-name').forEach(el => {
            el.style.color = colors.victim;
        });
        
        document.querySelectorAll('.distance-info').forEach(el => {
            el.style.color = colors.distance;
        });
    }

    updatePreview() {
        const colors = this.getColorsFromMenu();
        
        // Update preview elements
        document.querySelector('.killer-preview').style.color = colors.killer;
        document.querySelector('.victim-preview').style.color = colors.victim;
        document.querySelector('.weapon-preview').style.color = colors.weapon;
        document.querySelector('.distance-preview').style.color = colors.distance;
        document.querySelector('.border-preview').style.borderColor = colors.border;
        
        // Update preview killfeed
        const preview = document.querySelector('.killfeed-preview');
        preview.style.background = colors.background;
        preview.style.borderColor = colors.border;
        
        const previewEntry = document.querySelector('.preview-entry');
        previewEntry.style.borderLeftColor = colors.border;
        
        document.querySelector('.preview-entry .killer-name').style.color = colors.killer;
        document.querySelector('.preview-entry .victim-name').style.color = colors.victim;
        document.querySelector('.preview-entry .distance-info').style.color = colors.distance;
    }

    applyPreset(presetName) {
        const presets = {
            default: {
                background: "rgba(0, 0, 0, 0.8)",
                killer: "#00FF00",
                victim: "#FF0000",
                weapon: "#FFFFFF",
                distance: "#FFD700",
                border: "#333333"
            },
            dark: {
                background: "rgba(26, 26, 26, 0.9)",
                killer: "#4CAF50",
                victim: "#F44336",
                weapon: "#E0E0E0",
                distance: "#FFC107",
                border: "#424242"
            },
            neon: {
                background: "rgba(255, 0, 110, 0.2)",
                killer: "#00FFFF",
                victim: "#FF1493",
                weapon: "#FFFFFF",
                distance: "#FFFF00",
                border: "#8338EC"
            },
            military: {
                background: "rgba(45, 80, 22, 0.8)",
                killer: "#90EE90",
                victim: "#CD5C5C",
                weapon: "#F5F5DC",
                distance: "#FFD700",
                border: "#556B2F"
            }
        };
        
        if (presets[presetName]) {
            this.loadColorsToMenu(presets[presetName]);
            this.updatePreview();
        }
    }

    // Custom Images
    handleImageUpload(event) {
        const files = event.target.files;
        
        for (let file of files) {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.addCustomImage(file.name, e.target.result);
                };
                reader.readAsDataURL(file);
            }
        }
    }

    addCustomImage(filename, dataUrl) {
        this.customImages.set(filename, dataUrl);
        
        const container = document.getElementById('uploaded-images');
        const imageDiv = document.createElement('div');
        imageDiv.className = 'uploaded-image';
        
        const img = document.createElement('img');
        img.src = dataUrl;
        img.alt = filename;
        
        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-image';
        removeBtn.innerHTML = '×';
        removeBtn.onclick = () => {
            this.customImages.delete(filename);
            container.removeChild(imageDiv);
        };
        
        imageDiv.appendChild(img);
        imageDiv.appendChild(removeBtn);
        container.appendChild(imageDiv);
    }

    loadCustomImages() {
        // Load any saved custom images from localStorage
        const saved = localStorage.getItem('killfeed_custom_images');
        if (saved) {
            try {
                const images = JSON.parse(saved);
                for (let [filename, dataUrl] of Object.entries(images)) {
                    this.customImages.set(filename, dataUrl);
                    this.addCustomImage(filename, dataUrl);
                }
            } catch (e) {
                console.error('Failed to load custom images:', e);
            }
        }
    }

    saveCustomImages() {
        const images = Object.fromEntries(this.customImages);
        localStorage.setItem('killfeed_custom_images', JSON.stringify(images));
    }

    playSound(soundFile) {
        const audio = document.getElementById(soundFile.replace('.mp3', '-sound'));
        if (audio) {
            audio.currentTime = 0;
            audio.play().catch(e => console.log('Sound play failed:', e));
        }
    }
}

// Helper function for FiveM resource name
function GetParentResourceName() {
    return window.location.hostname === 'nui-game-internal' ? 
           'advanced_killfeed' : 'advanced_killfeed';
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.killfeed = new AdvancedKillfeed();
});
