/* ========================================
   ADVANCED KILLFEED STYLES
   ======================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: transparent;
    overflow: hidden;
    user-select: none;
}

/* ========================================
   KILLFEED CONTAINER
   ======================================== */

.killfeed-container {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 400px;
    max-height: 500px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    border: 2px solid #333;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
    z-index: 1000;
}

.killfeed-container.hidden {
    opacity: 0;
    transform: translateX(100%);
}

.killfeed-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 10px 10px 0 0;
    color: white;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.killfeed-header i {
    font-size: 16px;
}

.killfeed-list {
    padding: 8px;
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #666 transparent;
}

.killfeed-list::-webkit-scrollbar {
    width: 4px;
}

.killfeed-list::-webkit-scrollbar-track {
    background: transparent;
}

.killfeed-list::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 2px;
}

/* ========================================
   KILL ENTRY STYLES
   ======================================== */

.kill-entry {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    margin-bottom: 6px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid #ff6b6b;
    transition: all 0.3s ease;
    animation: slideInRight 0.5s ease;
}

.kill-entry:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-2px);
}

.kill-entry.suicide {
    border-left-color: #ffa502;
}

.kill-entry.headshot {
    border-left-color: #ff3838;
    box-shadow: 0 0 10px rgba(255, 56, 56, 0.3);
}

.kill-entry.noscope {
    border-left-color: #3742fa;
}

.kill-entry.driveby {
    border-left-color: #2ed573;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.kill-entry.fade-out {
    animation: fadeOut 0.5s ease forwards;
}

/* Player Names */
.player-name {
    font-weight: 500;
    font-size: 13px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.killer-name {
    color: #00ff00;
}

.victim-name {
    color: #ff0000;
}

/* Weapon Info */
.weapon-info {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0 8px;
}

.weapon-icon {
    width: 24px;
    height: 24px;
    filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.8));
}

.headshot-icon,
.noscope-icon,
.driveby-icon {
    font-size: 12px;
    color: #ffd700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.headshot-icon {
    color: #ff3838;
}

.noscope-icon {
    color: #3742fa;
}

.driveby-icon {
    color: #2ed573;
}

/* Distance Info */
.distance-info {
    font-size: 11px;
    color: #ffd700;
    font-weight: 600;
    background: rgba(255, 215, 0, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 6px;
}

/* ========================================
   COLOR MENU MODAL
   ======================================== */

.color-menu-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    transition: all 0.3s ease;
}

.color-menu-modal.hidden {
    opacity: 0;
    visibility: hidden;
}

.color-menu-container {
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    background: linear-gradient(135deg, #1e1e1e, #2d2d2d);
    border-radius: 16px;
    border: 2px solid #444;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
    overflow: hidden;
    animation: modalSlideIn 0.4s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.color-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.color-menu-header h2 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: 600;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.color-menu-content {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #666 transparent;
}

.color-menu-content::-webkit-scrollbar {
    width: 6px;
}

.color-menu-content::-webkit-scrollbar-track {
    background: transparent;
}

.color-menu-content::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 3px;
}

/* Color Sections */
.color-section {
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.color-section label {
    display: block;
    color: #fff;
    font-weight: 500;
    margin-bottom: 10px;
    font-size: 14px;
}

.color-input-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.color-picker {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    background: transparent;
}

.opacity-slider {
    flex: 1;
    height: 6px;
    background: linear-gradient(to right, transparent, #fff);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
}

.opacity-label {
    color: #ccc;
    font-size: 12px;
    min-width: 35px;
}

.color-preview {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Preset Colors */
.preset-section {
    margin-top: 24px;
}

.preset-section h3 {
    color: #fff;
    margin-bottom: 16px;
    font-size: 16px;
}

.preset-colors {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.preset-color {
    text-align: center;
    cursor: pointer;
    padding: 12px;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.preset-color:hover {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

.preset-preview {
    width: 100%;
    height: 40px;
    border-radius: 6px;
    margin-bottom: 8px;
}

.default-theme {
    background: linear-gradient(135deg, #000, #333);
}

.dark-theme {
    background: linear-gradient(135deg, #1a1a1a, #0d0d0d);
}

.neon-theme {
    background: linear-gradient(135deg, #ff006e, #8338ec);
}

.military-theme {
    background: linear-gradient(135deg, #2d5016, #4a7c59);
}

.preset-color span {
    color: #ccc;
    font-size: 12px;
}

/* Custom Images */
.custom-images-section {
    margin-top: 24px;
}

.custom-images-section h3 {
    color: #fff;
    margin-bottom: 16px;
    font-size: 16px;
}

.image-upload-area {
    border: 2px dashed #666;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.image-upload-area:hover {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.05);
}

#custom-image-upload {
    display: none;
}

.upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #ccc;
    cursor: pointer;
}

.upload-label i {
    font-size: 24px;
    color: #666;
}

.uploaded-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    margin-top: 16px;
}

.uploaded-image {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid #444;
}

.uploaded-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(255, 0, 0, 0.8);
    color: white;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 10px;
}

/* Preview Section */
.preview-section {
    margin-top: 24px;
    padding: 16px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
}

.preview-section h3 {
    color: #fff;
    margin-bottom: 16px;
    font-size: 16px;
}

.killfeed-preview {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #333;
}

.preview-entry {
    margin-bottom: 0;
    animation: none;
}

/* Footer */
.color-menu-footer {
    display: flex;
    justify-content: space-between;
    padding: 20px 24px;
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff5252, #e55100);
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ccc;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .killfeed-container {
        width: 90%;
        right: 5%;
    }

    .color-menu-container {
        width: 95%;
        margin: 20px;
    }

    .preset-colors {
        grid-template-columns: repeat(2, 1fr);
    }
}
