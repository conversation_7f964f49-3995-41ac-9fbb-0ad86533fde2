Config = {}

-- ========================================
-- ADVANCED KILLFEED CONFIGURATION
-- ========================================

-- General Settings
Config.EnableKillfeed = true                    -- تشغيل/إيقاف الكيل فيد
Config.ShowTime = 8000                          -- مدة عرض الكيل (بالميلي ثانية)
Config.MaxKillLines = 6                         -- عدد أقصى من خطوط الكيل المعروضة
Config.FadeAnimation = true                     -- تشغيل الأنيميشن

-- Position Settings (إعدادات الموضع)
Config.Position = "left"                        -- "left" أو "right" - موضع الكيل فيد
Config.TopOffset = 20                           -- المسافة من الأعلى (بكسل)
Config.SideOffset = 20                          -- المسافة من الجانب (بكسل)

-- Distance Settings (إعدادات المسافة)
Config.ShowDistance = true                      -- عرض المسافة
Config.DistanceUnit = "m"                       -- وحدة المسافة (m = متر, ft = قدم)
Config.MaxDistanceShow = 500                    -- أقصى مسافة لعرضها (متر)
Config.DistanceColor = "#FFD700"                -- لون نص المسافة

-- Color Menu Settings (إعدادات قائمة الألوان)
Config.ColorMenuKey = "o"                       -- زر فتح قائمة الألوان
Config.ColorMenuPermission = "server.owner"            -- الصلاحية المطلوبة (admin, moderator, vip, all)
Config.EnableColorMenu = true                   -- تشغيل قائمة الألوان

-- Custom Images Settings (إعدادات الصور المخصصة)
Config.EnableCustomImages = true                -- تشغيل الصور المخصصة
Config.CustomImagePath = "html/images/custom/"  -- مسار الصور المخصصة
Config.AllowImageUpload = true                  -- السماح برفع الصور

-- Kill Types (أنواع القتل)
Config.ShowHeadshot = true                      -- عرض الهيدشوت
Config.ShowNoscope = true                       -- عرض النوسكوب
Config.ShowDriveBy = true                       -- عرض القتل من السيارة
Config.ShowSuicide = true                       -- عرض الانتحار
Config.ShowMelee = true                         -- عرض القتل بالسلاح الأبيض

-- Player Settings (إعدادات اللاعبين)
Config.ShowPlayerKills = true                   -- عرض قتل اللاعبين
Config.ShowNPCKills = false                     -- عرض قتل الـ NPCs
Config.ShowAnimalKills = false                  -- عرض قتل الحيوانات

-- Sound Settings (إعدادات الصوت)
Config.EnableSounds = true                      -- تشغيل الأصوات
Config.KillSound = "kill_sound.mp3"             -- صوت القتل
Config.HeadshotSound = "headshot_sound.mp3"     -- صوت الهيدشوت

-- Default Colors (الألوان الافتراضية)
Config.DefaultColors = {
    background = "rgba(0, 0, 0, 0.8)",          -- خلفية الكيل فيد
    killer = "#00FF00",                          -- لون اسم القاتل
    victim = "#FF0000",                          -- لون اسم الضحية
    weapon = "#FFFFFF",                          -- لون السلاح
    distance = "#FFD700",                        -- لون المسافة
    border = "#333333"                           -- لون الحدود
}

-- Weapon Categories (فئات الأسلحة)
Config.WeaponCategories = {
    ["WEAPON_PISTOL"] = { name = "Pistol", icon = "pistol.png", category = "handgun" },
    ["WEAPON_COMBATPISTOL"] = { name = "Combat Pistol", icon = "combat_pistol.png", category = "handgun" },
    ["WEAPON_APPISTOL"] = { name = "AP Pistol", icon = "ap_pistol.png", category = "handgun" },
    ["WEAPON_PISTOL50"] = { name = "Pistol .50", icon = "pistol50.png", category = "handgun" },
    
    ["WEAPON_MICROSMG"] = { name = "Micro SMG", icon = "micro_smg.png", category = "smg" },
    ["WEAPON_SMG"] = { name = "SMG", icon = "smg.png", category = "smg" },
    ["WEAPON_ASSAULTSMG"] = { name = "Assault SMG", icon = "assault_smg.png", category = "smg" },
    
    ["WEAPON_ASSAULTRIFLE"] = { name = "Assault Rifle", icon = "assault_rifle.png", category = "rifle" },
    ["WEAPON_CARBINERIFLE"] = { name = "Carbine Rifle", icon = "carbine_rifle.png", category = "rifle" },
    ["WEAPON_ADVANCEDRIFLE"] = { name = "Advanced Rifle", icon = "advanced_rifle.png", category = "rifle" },
    
    ["WEAPON_SNIPERRIFLE"] = { name = "Sniper Rifle", icon = "sniper_rifle.png", category = "sniper" },
    ["WEAPON_HEAVYSNIPER"] = { name = "Heavy Sniper", icon = "heavy_sniper.png", category = "sniper" },
    ["WEAPON_MARKSMANRIFLE"] = { name = "Marksman Rifle", icon = "marksman_rifle.png", category = "sniper" },
    
    ["WEAPON_PUMPSHOTGUN"] = { name = "Pump Shotgun", icon = "pump_shotgun.png", category = "shotgun" },
    ["WEAPON_SAWNOFFSHOTGUN"] = { name = "Sawed-Off Shotgun", icon = "sawnoff_shotgun.png", category = "shotgun" },
    ["WEAPON_ASSAULTSHOTGUN"] = { name = "Assault Shotgun", icon = "assault_shotgun.png", category = "shotgun" },
    
    ["WEAPON_GRENADE"] = { name = "Grenade", icon = "grenade.png", category = "explosive" },
    ["WEAPON_RPG"] = { name = "RPG", icon = "rpg.png", category = "explosive" },
    ["WEAPON_STICKYBOMB"] = { name = "Sticky Bomb", icon = "sticky_bomb.png", category = "explosive" },
    
    ["WEAPON_KNIFE"] = { name = "Knife", icon = "knife.png", category = "melee" },
    ["WEAPON_BAT"] = { name = "Baseball Bat", icon = "bat.png", category = "melee" },
    ["WEAPON_CROWBAR"] = { name = "Crowbar", icon = "crowbar.png", category = "melee" },
    
    ["WEAPON_UNARMED"] = { name = "Fists", icon = "fists.png", category = "melee" },
    ["WEAPON_RUN_OVER_BY_CAR"] = { name = "Vehicle", icon = "vehicle.png", category = "vehicle" },
    ["WEAPON_RAMMED_BY_CAR"] = { name = "Vehicle Ram", icon = "vehicle_ram.png", category = "vehicle" }
}

-- Permission Levels (مستويات الصلاحيات)
Config.PermissionLevels = {
    ["all"] = 0,        -- جميع اللاعبين
    ["vip"] = 1,        -- VIP
    ["moderator"] = 2,  -- مشرف
    ["admin"] = 3,      -- أدمن
    ["owner"] = 4       -- مالك السيرفر
}

-- Debug Settings (إعدادات التطوير)
Config.Debug = false                            -- تشغيل وضع التطوير
Config.DebugPrint = false                       -- طباعة رسائل التطوير
